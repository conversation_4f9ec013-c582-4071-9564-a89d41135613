
import Link from "next/link";

export default function Home() {
  return (
    <main className="min-h-screen bg-white text-gray-900">
      <header className="sticky top-0 z-10 bg-white/80 backdrop-blur border-b">
        <div className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-blue-600 text-white text-sm font-bold">RB</span>
            <span className="text-sm sm:text-base font-semibold">Residencia Médica Boliviana</span>
          </div>
          <nav className="hidden sm:flex items-center gap-4 text-sm">
            <Link href="/inicio" className="hover:text-blue-700">Inicio</Link>
            <Link href="/planeador" className="hover:text-blue-700">Planeador</Link>
            <Link href="/upload" className="hover:text-blue-700">Subir</Link>
            <Link href="/sign-in" className="rounded-md border px-3 py-1.5 hover:bg-gray-50">Entrar</Link>
            <Link href="/sign-up" className="rounded-md bg-blue-600 text-white px-3 py-1.5 hover:bg-blue-700">Crear cuenta</Link>
          </nav>
        </div>
      </header>

      <section className="max-w-6xl mx-auto px-6 py-16 sm:py-24 grid md:grid-cols-2 gap-10 items-center">
        <div>
          <h1 className="text-4xl sm:text-5xl font-bold tracking-tight">Residencia Médica Boliviana</h1>
          <p className="mt-4 text-lg text-gray-600">
            Prepárate con preguntas estilo examen, resúmenes de alto rendimiento y simulacros. Todo en un solo lugar.
          </p>
          <div className="mt-6 flex flex-wrap gap-3">
            <Link href="/inicio" className="rounded-md bg-blue-600 text-white px-5 py-3 text-sm font-medium hover:bg-blue-700">
              Comenzar ahora
            </Link>
            <Link href="/lecciones/medicina-interna/diabetes" className="rounded-md border border-gray-300 px-5 py-3 text-sm font-medium hover:bg-gray-50">
              Ver una lección
            </Link>
          </div>
          <div className="mt-6 flex items-center gap-6 text-sm text-gray-500">
            <div className="flex items-center gap-2"><span className="text-blue-600">●</span> Modo claro</div>
            <div className="flex items-center gap-2">✓ Gratis para empezar</div>
          </div>
        </div>

        <div className="relative">
          <div className="aspect-[4/3] w-full rounded-xl border bg-gradient-to-br from-blue-50 to-indigo-50 p-1">
            <div className="h-full w-full rounded-lg bg-white p-6 grid grid-cols-2 gap-4">
              <div className="rounded-md border p-4">
                <div className="text-xs text-gray-500">Medicina Interna</div>
                <div className="mt-2 h-2 w-3/4 rounded bg-blue-100" />
                <div className="mt-2 h-2 w-1/2 rounded bg-blue-100" />
              </div>
              <div className="rounded-md border p-4">
                <div className="text-xs text-gray-500">Pediatría</div>
                <div className="mt-2 h-2 w-2/3 rounded bg-indigo-100" />
                <div className="mt-2 h-2 w-1/3 rounded bg-indigo-100" />
              </div>
              <div className="rounded-md border p-4">
                <div className="text-xs text-gray-500">Cirugía</div>
                <div className="mt-2 h-2 w-2/3 rounded bg-blue-100" />
                <div className="mt-2 h-2 w-1/4 rounded bg-blue-100" />
              </div>
              <div className="rounded-md border p-4">
                <div className="text-xs text-gray-500">Gineco-Obstetricia</div>
                <div className="mt-2 h-2 w-1/2 rounded bg-indigo-100" />
                <div className="mt-2 h-2 w-1/4 rounded bg-indigo-100" />
              </div>
            </div>
          </div>
          <div className="absolute -bottom-4 -right-4 hidden sm:block rounded-lg bg-white border shadow p-3">
            <div className="text-xs text-gray-500">Progreso</div>
            <div className="mt-2 h-2 w-32 bg-gray-100 rounded">
              <div className="h-2 bg-blue-600 rounded" style={{ width: "48%" }} />
            </div>
          </div>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-6 py-8 sm:py-12">
        <h2 className="text-xl font-semibold">Especialidades</h2>
        <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/lecciones/medicina-interna/diabetes" className="group rounded-lg border p-5 hover:shadow-sm">
            <div className="text-2xl">🫀</div>
            <div className="mt-2 font-medium">Medicina Interna</div>
            <div className="text-sm text-gray-500">Diabetes, HTA y más</div>
          </Link>
          <div className="rounded-lg border p-5 opacity-60">
            <div className="text-2xl">🩺</div>
            <div className="mt-2 font-medium">Pediatría</div>
            <div className="text-sm text-gray-500">Próximamente</div>
          </div>
          <div className="rounded-lg border p-5 opacity-60">
            <div className="text-2xl">🩻</div>
            <div className="mt-2 font-medium">Cirugía</div>
            <div className="text-sm text-gray-500">Próximamente</div>
          </div>
          <div className="rounded-lg border p-5 opacity-60">
            <div className="text-2xl">🤰</div>
            <div className="mt-2 font-medium">Gineco-Obstetricia</div>
            <div className="text-sm text-gray-500">Próximamente</div>
          </div>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-6 py-12 grid sm:grid-cols-3 gap-6">
        <div className="rounded-lg border p-6">
          <div className="text-lg font-semibold">Preguntas tipo examen</div>
          <p className="mt-1 text-sm text-gray-600">Practica con bancos diseñados para el contexto boliviano.</p>
        </div>
        <div className="rounded-lg border p-6">
          <div className="text-lg font-semibold">Resúmenes útiles</div>
          <p className="mt-1 text-sm text-gray-600">Puntos clave y algoritmos de manejo.</p>
        </div>
        <div className="rounded-lg border p-6">
          <div className="text-lg font-semibold">Simulacros</div>
          <p className="mt-1 text-sm text-gray-600">Mide tu progreso con simulacros cronometrados.</p>
        </div>
      </section>

      <footer className="border-t">
        <div className="max-w-6xl mx-auto px-6 py-8 text-sm text-gray-500 flex items-center justify-between">
          <span>© {new Date().getFullYear()} Residencia Médica Boliviana</span>
          <div className="flex gap-4">
            <Link href="/sign-in" className="hover:text-gray-700">Entrar</Link>
            <Link href="/sign-up" className="hover:text-gray-700">Registro</Link>
          </div>
        </div>
      </footer>
    </main>
  );
}
