'use client'

import { User<PERSON>utton, useAuth, useUser } from '@clerk/nextjs';
import Link from 'next/link';

export default function TopProvider() {
  const { isSignedIn } = useAuth();
  const { user } = useUser();

  return (
    <header className="sticky top-0 z-50 bg-white/80 backdrop-blur border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo on the left */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center gap-3">
              <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-600 text-white font-bold text-lg">
                RB
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold text-gray-900">
                  Residencia Médica Boliviana
                </h1>
              </div>
            </Link>
          </div>

          {/* Navigation and User section on the right */}
          <div className="flex items-center gap-4">
            {/* Navigation links */}
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/inicio"
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                Inicio
              </Link>
              <Link
                href="/lecciones"
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                Lecciones
              </Link>
              <Link
                href="/planeador"
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                Planeador
              </Link>
              <Link
                href="/upload"
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                Subir
              </Link>
            </nav>

            {/* User section */}
            {isSignedIn ? (
              <div className="flex items-center gap-3">
                {/* Welcome message */}
                <div className="hidden sm:block text-sm text-gray-600">
                  Hola, {user?.firstName || user?.emailAddresses[0]?.emailAddress?.split('@')[0]}
                </div>

                {/* Clerk UserButton with avatar */}
                <UserButton
                  appearance={{
                    elements: {
                      avatarBox: "h-8 w-8",
                      userButtonPopoverCard: "bg-white border shadow-lg",
                      userButtonPopoverActions: "text-gray-700"
                    }
                  }}
                  userProfileMode="navigation"
                  userProfileUrl="/profile"
                />
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Link
                  href="/sign-in"
                  className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
                >
                  Entrar
                </Link>
                <Link
                  href="/sign-up"
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Crear cuenta
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
