'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

const links = [
  { href: '/inicio', label: 'Inicio' },
  { href: '/lecciones/medicina-interna', label: 'Lecciones' },
  { href: '/planeador', label: 'Planeador' },
  { href: '/upload', label: 'Subir' },
]

export default function TopNav() {
  const pathname = usePathname()

  return (
    <header className="sticky top-0 z-20 bg-white/90 backdrop-blur border-b">
      <div className="max-w-6xl mx-auto px-6 py-3 flex items-center justify-between">
        <Link href="/" className="flex items-center gap-2">
          <span className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-blue-600 text-white text-sm font-bold">RB</span>
          <span className="text-sm sm:text-base font-semibold text-gray-900">Residencia Médica Boliviana</span>
        </Link>

        <nav className="hidden sm:flex items-center gap-4 text-sm">
          {links.map(({ href, label }) => {
            const active = pathname?.startsWith(href)
            return (
              <Link
                key={href}
                href={href}
                className={`px-1.5 py-1 border-b-2 transition-colors ${
                  active ? 'border-blue-600 text-blue-700' : 'border-transparent text-gray-600 hover:text-gray-800'
                }`}
              >
                {label}
              </Link>
            )
          })}
        </nav>

        <div className="hidden sm:flex items-center gap-2">
          <Link href="/sign-in" className="text-sm text-gray-700 hover:text-gray-900 px-3 py-1.5">Entrar</Link>
          <Link href="/sign-up" className="text-sm rounded-md bg-blue-600 text-white px-3 py-1.5 hover:bg-blue-700">Crear cuenta</Link>
        </div>
      </div>
    </header>
  )
}
