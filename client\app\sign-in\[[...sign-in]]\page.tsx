'use client'
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { SignIn, useAuth } from '@clerk/nextjs';

export default function SignInPage() {
  const router = useRouter();
  const { isSignedIn } = useAuth();

  useEffect(() => {
    if (isSignedIn) {
      router.push('/inicio');
    }
  }, [isSignedIn, router]);

  return (
  <div className="flex items-center justify-center min-h-screen">
    <SignIn 
      routing="path" 
      path="/sign-in" 
      signUpUrl="/sign-up"
      forceRedirectUrl="/inicio"
    />
  </div>
  );
}
