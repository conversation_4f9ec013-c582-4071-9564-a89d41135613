{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "96dpS1azgl9CScCtIzPvTVZYmNUzQ94jpaW1OC3gyj4=", "__NEXT_PREVIEW_MODE_ID": "2a0f5b9ac9483b66d44f67d60f4987dc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3003d4a512bb5514cd6818f2cc417bf844a3733aea5b0f651b2fbbfe4f082ed8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "39c051238576f8d6d47bca4f4bcc1c5422dfefcd6bc5e5026e845515908cd018"}}}, "functions": {}, "sortedMiddleware": ["/"]}