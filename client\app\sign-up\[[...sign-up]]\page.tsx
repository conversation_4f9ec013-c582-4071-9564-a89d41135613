'use client'
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { SignUp, useAuth } from '@clerk/nextjs';

export default function SignUpPage() {
  const router = useRouter();
  const { isSignedIn } = useAuth();

  useEffect(() => {
    if (isSignedIn) {
      router.push('/inicio');
    }
  }, [isSignedIn, router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignUp 
        routing="path" 
        path="/sign-up" 
        signInUrl="/sign-in"
        forceRedirectUrl="/inicio"
        appearance={{
          layout: {
            socialButtonsPlacement: "top",
            termsPageUrl: "/home/<USER>/terms-of-service",
            privacyPageUrl: "/home/<USER>/privacy-policy",
          }
        }}
        
      />
    </div>
  );
  
}