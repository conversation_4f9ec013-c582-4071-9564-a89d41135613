import { Search, LayoutDashboard, Grid3X3, User, Calendar, Scissors, Stethos<PERSON>, Baby, Users, Heart } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

import TopProvider from "@/providers/top-provider";

export default function Component() {
  return (
    <div className="min-h-screen bg-gray-50">
      <TopProvider />
      {/* Main Content */}
      <div className="p-6">
        {/* Welcome Section */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xl text-gray-700">Bienvenido Dr. Juan, Buenas Tardes.</h1>
          <Button variant="link" className="text-blue-500">Mi Progreso</Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Performance Cards */}
          <div className="lg:col-span-2 grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-700 mb-1">85%</div>
                <div className="text-sm text-gray-500 mb-1">Lecciones Completadas</div>
                <div className="text-xs text-gray-400">de 245</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-700 mb-1">78%</div>
                <div className="text-sm text-gray-500 mb-1">Evaluaciones</div>
                <div className="text-xs text-gray-400">de 156</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-700 mb-1">92</div>
                <div className="text-sm text-gray-500 mb-1">Puntaje de Simulacros</div>
                <div className="text-xs text-gray-400">promedio</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Calendar className="w-6 h-6 text-blue-500 mx-auto mb-3" />
                <div className="text-lg font-bold text-gray-700">12 días activo</div>
              </CardContent>
            </Card>
          </div>

          {/* Study Planner */}
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-800 text-lg mb-2">Planificador de Estudios</h3>
              <p className="text-sm text-gray-600">Organiza tu tiempo de estudio por especialidad</p>
              <Button className="mt-4 w-full bg-green-600 hover:bg-green-700">
                Crear Plan de Estudio
              </Button>
            </CardContent>
          </Card>

        </div>

        {/* Categories Section */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">Especialidades de Residencia</CardTitle>
              <div className="flex items-center gap-8 text-sm text-gray-500">
                <span>Lecciones</span>
                <span>Evaluaciones</span>
                <span>Progreso</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { 
                  name: "Cirugía General", 
                  icon: Scissors, 
                  lessons: 45, 
                  evaluations: 12, 
                  progress: "89%",
                  color: "text-red-500",
                  bgColor: "bg-red-50"
                },
                { 
                  name: "Medicina Interna", 
                  icon: Stethoscope, 
                  lessons: 67, 
                  evaluations: 18, 
                  progress: "92%",
                  color: "text-blue-500",
                  bgColor: "bg-blue-50"
                },
                { 
                  name: "Obstetricia y Ginecología", 
                  icon: Baby, 
                  lessons: 38, 
                  evaluations: 9, 
                  progress: "76%",
                  color: "text-pink-500",
                  bgColor: "bg-pink-50",
                  href: "/lecciones/ginecologia-obstetricia"
                },
                { 
                  name: "Pediatría", 
                  icon: Heart, 
                  lessons: 52, 
                  evaluations: 15, 
                  progress: "85%",
                  color: "text-green-500",
                  bgColor: "bg-green-50"
                },
                { 
                  name: "Salud Pública", 
                  icon: Users, 
                  lessons: 29, 
                  evaluations: 8, 
                  progress: "68%",
                  color: "text-purple-500",
                  bgColor: "bg-purple-50"
                }
              ].map((specialty, index) => (
                <Link key={index} href={specialty.href || "#"} passHref>
                  <div className="flex flex-col py-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded-lg px-2 transition-colors cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 ${specialty.bgColor} rounded-lg flex items-center justify-center`}>
                          <specialty.icon className={`w-6 h-6 ${specialty.color}`} />
                        </div>
                        <div>
                          <span className="font-medium text-gray-800">{specialty.name}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-12 text-sm">
                        <div className="text-center">
                          <div className="font-semibold text-gray-700">{specialty.lessons}</div>
                          <div className="text-gray-500">lecciones</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-700">{specialty.evaluations}</div>
                          <div className="text-gray-500">evaluaciones</div>
                        </div>
                        <div className="text-center min-w-[60px]">
                          <div className={`font-semibold ${specialty.color}`}>{specialty.progress}</div>
                          <div className="text-gray-500">completado</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>



        {/* Exam Simulators */}
        <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Exámenes Simulacro</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-green-900">ENARM - Medicina Interna</h4>
                    <span className="text-sm font-medium text-green-700 bg-green-100 px-2 py-1 rounded">Completado</span>
                  </div>
                  <p className="text-sm text-green-700 mb-3">Simulacro Nacional de Residencias</p>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-2xl font-bold text-green-800">87/100</p>
                      <p className="text-xs text-green-600">Calificación estimada</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-700">Percentil 92</p>
                      <p className="text-xs text-green-600">Posición nacional</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-blue-900">ENARM - Cirugía General</h4>
                    <span className="text-sm font-medium text-blue-700 bg-blue-100 px-2 py-1 rounded">En Progreso</span>
                  </div>
                  <p className="text-sm text-blue-700 mb-3">Disponible hasta: 20 de Marzo</p>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-2xl font-bold text-blue-800">45/100</p>
                      <p className="text-xs text-blue-600">Progreso actual</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-700">45% completado</p>
                      <div className="w-20 bg-blue-200 rounded-full h-2 mt-1">
                        <div className="bg-blue-600 h-2 rounded-full" style={{width: '45%'}}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
      </div>
    </div>
  )
}
